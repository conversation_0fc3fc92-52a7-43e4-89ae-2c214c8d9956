<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
      <el-form-item label="一类指标" prop="primaryIndicator">
        <el-select v-model="queryParams.primaryIndicator" clearable filterable style="width: 180px" @change="changePrimaryIndicator">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in primaryIndicatorOptions" :key="item.code" :value="item.code" :label="item.name"/>
        </el-select>
      </el-form-item>
      <el-form-item label="二类指标" prop="secondaryIndicator">
        <el-select v-model="queryParams.secondaryIndicator" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in secondaryIndicatorOptions" :key="item.code" :value="item.code" :label="item.name" v-if="!queryParams.primaryIndicator || item.primaryIndicator === queryParams.primaryIndicator"/>
        </el-select>
      </el-form-item>
      <el-form-item label="项管审核状态" prop="projectManagerAuditStatus">
        <el-select v-model="queryParams.projectManagerAuditStatus" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.performance_feedback_audit_status" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="最终审核状态" prop="finalAudit">
        <el-select v-model="queryParams.finalAudit" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.performance_feedback_audit_status" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="绩效级别" prop="recommendedLevel">
        <el-select v-model="queryParams.recommendedLevel" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.performance_level" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="年份" prop="year">
        <el-select v-model="queryParams.year" placeholder="选择年份" >
          <el-option
            v-for="year in yearOptions"
            :key="year"
            :label="year"
            :value="year">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="月份" prop="month">
        <el-select v-model="queryParams.month" clearable>
          <el-option
            v-for="dict in dict.type.month"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="nickName">
        <el-input v-model="queryParams.nickName" placeholder="姓名" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="岗位" prop="personType">
        <el-select v-model="queryParams.personType" clearable filterable style="width: 180px">
          <el-option v-for="item in dict.type.person_type" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="所属组" prop="groupId">
        <el-select v-model="queryParams.groupId" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in groupDict" :key="item.deptId" :value="item.deptId" :label="item.deptName"/>
        </el-select>
      </el-form-item>
      <el-form-item label="数据来源" prop="dataSource">
        <el-select v-model="queryParams.dataSource" clearable filterable style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option v-for="item in dict.type.performance_feedback_data_source" :key="item.value" :value="item.value" :label="item.label"/>
        </el-select>
      </el-form-item>
      <el-form-item label="事件发生时间" prop="eventTimeRange">
        <el-date-picker v-model="queryParams.eventTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="提交时间" prop="submitTimeRange">
        <el-date-picker v-model="queryParams.submitTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="项管审核时间" prop="projectManagerAuditTimeRange">
        <el-date-picker v-model="queryParams.projectManagerAuditTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="最终审核时间" prop="finalAuditTimeRange">
        <el-date-picker v-model="queryParams.finalAuditTimeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange"
          :default-time="['00:00:00', '23:59:59']" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="是否已取消" prop="isCanceled">
        <el-select v-model="queryParams.isCanceled" clearable filterable placeholder="全部" style="width: 180px">
          <el-option :value="null" label="全部"/>
          <el-option :value="1" label="已取消"/>
          <el-option :value="0" label="未取消"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['system:performanceFeedback:batchCancel']"
          :disabled="multiple"
          :loading="cancelLoading"
          icon="el-icon-close"
          plain
          size="mini"
          type="danger"
          @click="handleCancel"
        >取消记录</el-button>
      </el-col>
      <el-col :span="2">
        <el-button
          type="primary"
          size="mini"
          :disabled="multiple"
          @click="handleBatchFinalAudit"
          v-hasPermi="['system:performanceFeedbackMain:batchFinalAudit']"
        >批量最终审核</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="table" v-loading="loading" :data="tableData" border height="calc(100vh - 380px)" stripe @selection-change="handleSelectionChange" @sort-change="handleSortChange">
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column type="index" label="#" width="55" align="center" />
      <el-table-column label="反馈编码" align="center" prop="feedbackCode">
        <template slot-scope="scope">
          <span :class="{ 'red-feedback-code': isLowPerformance(scope.row.recommendedLevel) }">
            {{ scope.row.feedbackCode }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否已取消" min-width="100" prop="isCanceled">
        <template slot-scope="scope">
          <el-tag :type="normalizeYes(scope.row.isCanceled) ? 'danger' : 'success'">
            {{ normalizeYes(scope.row.isCanceled) ? '已取消' : '未取消' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="项管审核状态" align="center" prop="projectManagerAuditStatus" sortable="custom">
        <template slot-scope="scope">
          <el-tag :type="statusTagType(scope.row.projectManagerAuditStatus)" effect="dark">
            {{ getDictLabel(scope.row.projectManagerAuditStatus, dict.type.performance_feedback_audit_status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="最终审核状态" align="center" prop="finalAudit" sortable="custom">
        <template slot-scope="scope">
          <el-tag :type="statusTagType(scope.row.finalAudit)" effect="dark">
            {{ getDictLabel(scope.row.finalAudit, dict.type.performance_feedback_audit_status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="一类指标" align="center" prop="primaryIndicator">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.primaryIndicator, primaryIndicatorOptions, 'code', 'name') }}
        </template>
      </el-table-column>
      <el-table-column label="二类指标" align="center" prop="secondaryIndicator">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.secondaryIndicator, secondaryIndicatorOptions, 'code', 'name') }}
        </template>
      </el-table-column>
      <el-table-column label="事件标题" align="center" prop="eventTitle" min-width="160" />
      <el-table-column label="事件明细" align="center" prop="eventDetail" min-width="160">
        <template slot-scope="scope">
          <div v-if="!scope.row.eventDetail || scope.row.eventDetail.length < 30">
            {{ scope.row.eventDetail }}
          </div>
          <el-tooltip v-else :content="scope.row.eventDetail" placement="top">
            <div class="ellipsis-multiline">
              {{ scope.row.eventDetail }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="事件发生时间" align="center" min-width="160">
        <template slot-scope="scope">
          <span v-if="scope.row.eventStartTime">{{ scope.row.eventStartTime }}</span>
          <span v-if="scope.row.eventEndTime">-{{ scope.row.eventEndTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属组" align="center" prop="groupName" />
      <el-table-column label="岗位" align="center" prop="personType">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.personType, dict.type.person_type) }}
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center" prop="nickName" />
      <el-table-column label="推荐绩效级别" align="center" prop="recommendedLevel" min-width="100" />
      <el-table-column label="推荐原因" align="center" prop="recommendedReason" min-width="160">
        <template slot-scope="scope">
          <div v-if="!scope.row.recommendedReason || scope.row.recommendedReason.length < 30">
            {{ scope.row.recommendedReason }}
          </div>
          <el-tooltip v-else :content="scope.row.recommendedReason" placement="top">
            <div class="ellipsis-multiline">
              {{ scope.row.recommendedReason }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="数据来源" align="center" prop="dataSource">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.dataSource, dict.type.performance_feedback_data_source) }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="feedbackTime" min-width="100" sortable="custom" />
      <el-table-column label="提交时间" align="center" prop="submitTime" min-width="100" sortable="custom" />
      <el-table-column label="项管审核人" align="center" prop="projectManagerAuditor">
        <template slot-scope="scope">
          {{ getDictLabel(scope.row.projectManagerAuditor, dict.type.project_outcome_project_manager) }}
        </template>
      </el-table-column>
      <el-table-column label="项管审核时间" align="center" prop="projectManagerAuditTime" min-width="100" sortable="custom" />
      <el-table-column label="项管审核意见" align="center" prop="projectManagerRemark" min-width="160" />
      <el-table-column label="最终审核意见" align="center" prop="finalRemark" min-width="160" />
      <el-table-column label="最终审核时间" align="center" prop="finalAuditTime" min-width="100" sortable="custom" />
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 批量最终审核弹窗 -->
    <feedbackAuditDialog
      :dialogVisible.sync="feedbackAuditDialog.show"
      :dialogType="feedbackAuditDialog.type"
      :ids="feedbackAuditDialog.ids"
      @callback="handleQuery"
    />
  </div>
</template>

<script>
import { performanceFeedbackList, indicatorList, feedbackBatchCancel } from "@/api/business/performanceFeedback"
import { deptSelect } from "../../../api/commonBiz"
import feedbackAuditDialog from '../feedbackManagement/components/feedbackAuditDialog.vue'
export default {
  name: "FeedbackDetails",
  dicts: [
    'performance_feedback_audit_status',
    'performance_level',
    'person_type',
    'performance_feedback_data_source',
    'project_outcome_project_manager',
    'month'
  ],
  components: { feedbackAuditDialog },
  data() {
    let month = new Date().getMonth() + 1
    return {
      yearOptions: [],
      // 遮罩层
      loading: false,
      cancelLoading: false,
      // 总条数
      total: 0,
      // 数据库权限表格数据
      tableData: [],
      // 多选管理
      selectedIds: [],
      multiple: true,
      // 一类指标下拉数据
      primaryIndicatorOptions: [],
      // 二类指标下拉数据
      secondaryIndicatorOptions: [],
      // 所属组下拉数据
      groupDict: [],
      // 审核弹窗
      feedbackAuditDialog: {
        show: false,
        type: '',
        ids: []
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        primaryIndicator: null,
        secondaryIndicator: null,
        projectManagerAuditStatus: null,
        finalAudit: null,
        recommendedLevel: null,
        nickName: null,
        personType: null,
        groupId: null,
        dataSource: null,
        eventTimeRange: [], // 事件发生时间
        submitTimeRange: [], // 提交时间
        projectManagerAuditTimeRange: [], // 项管审核时间
        finalAuditTimeRange: [], // 最终审核时间
        year: new Date().getFullYear(), // 年
        month: month < 10 ? ('0' + month) : month, // 月
        isCanceled: null // 是否已取消
      },
      showSearch: true
    }
  },
  watch: {
    $route: {
      handler(route) {
        if (this.$route.params.params && this.$route.name === 'FeedbackDetails') {
          this.resetForm("queryForm")
          const queryParams = JSON.parse(this.$route.params.params || '{}')
          this.queryParams = Object.assign(this.queryParams, queryParams)
          this.handleQuery()
        }
      },
      deep: true
    }
  },
  mounted () {
    const currentYear = new Date().getFullYear()
    for (let i = 0; i < 5; i++) {
      this.yearOptions.push(currentYear - i)
    }
    if (this.$route.params.params && this.$route.name === 'FeedbackDetails') {
      this.resetForm("queryForm")
      const queryParams = JSON.parse(this.$route.params.params || '{}')
      this.queryParams = Object.assign(this.queryParams, queryParams)
    }
    this.handleQuery()
  },
  created() {
    this.indicatorList()
    this.getDeptList()
  },
  methods: {
    /** 查询数据库权限列表 */
    getList () {
      if (this.queryParams.eventTimeRange && this.queryParams.eventTimeRange.length !== 0) { // 事件发生时间
        this.queryParams.eventStartTime = this.queryParams.eventTimeRange[0]
        this.queryParams.eventEndTime = this.queryParams.eventTimeRange[1]
      } else {
        this.queryParams.eventStartTime = null
        this.queryParams.eventEndTime = null
      }
      if (this.queryParams.submitTimeRange && this.queryParams.submitTimeRange.length !== 0) { // 提交时间
        this.queryParams.submitTimeBegin = this.queryParams.submitTimeRange[0]
        this.queryParams.submitTimeEnd = this.queryParams.submitTimeRange[1]
      } else {
        this.queryParams.submitTimeBegin = null
        this.queryParams.submitTimeEnd = null
      }
      if (this.queryParams.projectManagerAuditTimeRange && this.queryParams.projectManagerAuditTimeRange.length !== 0) { // 项管审核时间
        this.queryParams.projectManagerAuditTimeBegin = this.queryParams.projectManagerAuditTimeRange[0]
        this.queryParams.projectManagerAuditTimeEnd = this.queryParams.projectManagerAuditTimeRange[1]
      } else {
        this.queryParams.projectManagerAuditTimeBegin = null
        this.queryParams.projectManagerAuditTimeEnd = null
      }
      if (this.queryParams.finalAuditTimeRange && this.queryParams.finalAuditTimeRange.length !== 0) { // 最终审核时间
        this.queryParams.finalAuditTimeBegin = this.queryParams.finalAuditTimeRange[0]
        this.queryParams.finalAuditTimeEnd = this.queryParams.finalAuditTimeRange[1]
      } else {
        this.queryParams.finalAuditTimeBegin = null
        this.queryParams.finalAuditTimeEnd = null
      }
      let params = {...this.queryParams}
      delete params.eventTimeRange
      delete params.submitTimeRange
      delete params.projectManagerAuditTimeRange
      delete params.finalAuditTimeRange
      this.loading = true
      performanceFeedbackList(params).then(res => {
        this.loading = false
        if (res.code === 200) {
          this.total = res.total
          this.tableData = res.rows
          this.$nextTick(() => {
            if (this.$refs.table) this.$refs.table.clearSelection()
            this.selectedIds = []
            this.multiple = true
          })
        } else {
          this.$message.error(res.msg)
        }
      }).catch(() => {
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    /** 根据value获取字典label */
    getDictLabel (value, dictData, valueKey, labelKey) {
      let dictValueKey = valueKey || 'value'
      let dictLabelKey = labelKey || 'label'
      const item = dictData.find(item => item[dictValueKey] === value)
      return item ? item[dictLabelKey] : value
    },
    /** 一类指标、二类指标下拉数据 */
    indicatorList () {
      indicatorList().then(res => {
        if (res.code === 200) {
          this.primaryIndicatorOptions = res.data
          let secondaryIndicatorOptions = []
          this.primaryIndicatorOptions.forEach(item => {
            secondaryIndicatorOptions.push(...item.secondaryIndicators.map(second => {
              return {
                code: second.code,
                name: second.name,
                primaryIndicator: item.code
              }
            }))
          })
          this.secondaryIndicatorOptions = secondaryIndicatorOptions
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /** 一类指标变更 */
    changePrimaryIndicator () {
      this.queryParams.secondaryIndicator = null
    },
    /** 查询部门列表 */
    getDeptList() {
      deptSelect().then(response => {
        this.groupDict = response.data
      })
    },
    /** 排序 */
    handleSortChange (column, prop, order) {
      this.queryParams.orderByColumn = column.prop
      this.queryParams.isAsc = column.order
      this.handleQuery()
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedIds = selection.map(item => item.id)
      this.multiple = selection.length === 0
    },
    /** 统一判断是否为“是” */
    normalizeYes(value) {
      if (value === true || value === 'true' || value === 1 || value === '1') return true
      return false
    },
    /** 状态对应的标签类型（参考 FeedbackManagement） */
    statusTagType(value) {
      if (value === 'NOT_AUDITED') return 'danger'
      if (value === 'AUDITED' || value === 'APPROVED') return 'warning'
      if (value === 'REJECTED' || value === 'DENIED') return 'info'
      return 'info'
    },
    /** 批量取消 */
    handleCancel() {
      if (!this.selectedIds || this.selectedIds.length === 0) return
      this.$modal.confirm('是否确认取消选中的记录？').then(() => {
        this.cancelLoading = true
        return feedbackBatchCancel({ ids: this.selectedIds })
      }).then(() => {
        this.$modal.msgSuccess('取消成功')
        this.handleQuery()
      }).catch(() => {
      }).finally(() => {
        this.cancelLoading = false
      })
    },
    /** 批量最终审核 */
    handleBatchFinalAudit() {
      if (!this.selectedIds || this.selectedIds.length === 0) return
      const selectedRows = this.tableData.filter(item => this.selectedIds.indexOf(item.id) >= 0)
      // 资格校验：需已项管同意且最终未审核
      const allEligible = selectedRows.every(row => row.projectManagerAuditStatus === 'APPROVED' && row.finalAudit === 'NOT_AUDITED')
      if (!allEligible) {
        this.$modal.msgError('仅限于项管审核状态为同意，最终审核状态为未审核时可进行最终审核')
        return
      }
      // ID 映射（优先使用主表ID）
      const ids = selectedRows.map(row => row.mainFeedbackId || row.id)
      this.feedbackAuditDialog.type = 'batchFinalAudit'
      this.feedbackAuditDialog.ids = ids
      this.feedbackAuditDialog.show = true
    },
    /** 判断是否为低绩效级别（C/D） */
    isLowPerformance (recommendedLevel) {
      if (!recommendedLevel) return false
      if (recommendedLevel) {
        return recommendedLevel === 'C' || recommendedLevel === 'D'
      }
      return false
    }
  }
}
</script>
<style>
/* 多行省略核心样式 */
.ellipsis-multiline {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3; /* 控制显示行数 */
  line-clamp: 3; /* 标准属性，增强兼容性 */
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5; /* 与行高保持一致 */
  max-height: calc(3 * 1.5em); /* 行高*行数 */
  word-break: break-all; /* 允许单词内断行 */
}
.el-tooltip__popper {
  max-width: 600px; /* 可根据实际情况调整 */
}

.red-feedback-code {
  color: #f56c6c;
  font-weight: bold;
}
</style>
