<template>
  <el-dialog title="请选择需要加入的成果" :visible.sync="visible" width="800px" @close="handleClose">
    <!-- 搜索区域 -->
    <div style="margin-bottom: 20px;">
      <el-input v-model="projectTaskName" placeholder="请输入项目名称进行搜索"
        style="width: 300px; margin-right: 10px;"
        clearable @keyup.enter.native="handleSearch">
        <i slot="suffix" class="el-input__icon el-icon-search"></i>
      </el-input>
      <el-button type="primary" @click="handleSearch">搜索</el-button>
    </div>
    <!-- 成果列表表格 -->
    <el-table ref="table" v-loading="loading"
        :data="resultList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        style="width: 100%"
        border
        height="400px"
        :row-key="getRowKey">
        <el-table-column type="selection" width="55" :reserve-selection="false"></el-table-column>
        <el-table-column label="成果编码" prop="resultCode" ></el-table-column>
        <el-table-column label="业务类型" prop="businessTypeName" ></el-table-column>
        <el-table-column label="项目名称" prop="projectTaskName" ></el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleConfirm" :loading="loading" :disabled="!selectedResults.length">保存</el-button>
      <el-button @click="handleClose">取消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { listByDoingOrNotStart, joinProjectResults } from "@/api/project/projectResult"

export default {
  name: "JoinResultDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
      return {
        loading: false,
        projectTaskName: '',
        resultList: [],
        selectedResults: [],
      }
  },
  watch: {
    visible(val) {
      if (val) {
        this.resetData()
        this.loadResultList()
      }
    }
  },
  methods: {
    resetData() {
      this.projectTaskName = ''
      this.selectedResults = []
    },
    // 获取行的唯一标识
    getRowKey(row) {
      return row.id || row.resultCode || row.projectTaskName
    },
    // 处理行选择
    handleRowClick(row, column, event) {
      if (column.type === 'selection') {
        return
      }
      this.$refs.table.toggleRowSelection(row)
    },
    handleSearch() {
      this.loadResultList()
    },
    //选择最后选择的
    handleSelectionChange(selection) {
      if (selection.length === 0) {
        this.selectedResults = []
      } else if (selection.length === 1) {
        this.selectedResults = selection
      } else {
        const lastSelected = selection[selection.length - 1]
        this.selectedResults = [lastSelected]
        this.$nextTick(() => {
          if (this.$refs.table) {
            this.$refs.table.clearSelection()
            this.$refs.table.toggleRowSelection(lastSelected, true)
          }
        })
      }
    },
    async loadResultList() {
      this.loading = true
      try {
        const response = await listByDoingOrNotStart({projectTaskName:this.projectTaskName})
        if (response.code === 200) {
          this.resultList = response.data || []
          // 数据更新后清除选择状态
          this.selectedResults = []
          this.$nextTick(() => {
            if (this.$refs.table) {
              this.$refs.table.clearSelection()
            }
          })
        } else {
          this.$message.error(response.msg || '获取成果列表失败')
        }
      } catch (error) {
        console.error('获取成果列表失败:', error)
        this.$message.error('获取成果列表失败')
      } finally {
        this.loading = false
      }
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    async handleConfirm() {
      if (this.selectedResults.length === 0) {
        this.$message.warning('请选择要加入的成果')
        return
      }
      // 调用加入成果接口
      this.callJoinApi()
    },
    // 调用加入成果接口
    async callJoinApi() {
      try {
        this.loading = true
        // 构建请求数据
        const requestData = {
          storyIdList: this.$parent.selectedRows.map(row => row.id),
          id: this.selectedResults[0].id
        }
        const response = await joinProjectResults(requestData)
        if (response.code === 200) {
          this.$message.success('加入成果成功')
          this.handleClose()
          // 通知父组件刷新列表
          this.$emit('confirm', this.selectedResults)
        } else {
          this.$message.error(response.msg || '加入成果失败')
        }
      } catch (error) {
        console.error('加入成果失败:', error)
      } finally {
        this.loading = false
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: right;
}
</style>
